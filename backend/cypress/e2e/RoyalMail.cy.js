describe('Royal Mail Login and Navigation', () => {
  it('should login and navigate to manifested orders', () => {
    // Visit the login page
    cy.visit('https://auth.parcel.royalmail.com/account/login');

    // Wait for the email and password input fields to be visible
    cy.get('input[name="Email"]').should('be.visible');
    cy.get('input[name="Password"]').should('be.visible');

    // Accept cookies
    cy.wait(2000);
    cy.get('body').then(($body) => {
      if ($body.find('#consent_prompt_submit').length > 0) {
        cy.get('#consent_prompt_submit').click();
      }
    });

    // Login
    cy.get('input[name="Email"]').type('<EMAIL>');
    cy.get('input[name="Password"]').type('28Karno1960');
    cy.get('button[type="submit"]').click();

    // Wait for navigation to the home page
    cy.url().should('eq', 'https://business.parcel.royalmail.com/');

    // Check for errors and reload if necessary
    cy.get('body').then(($body) => {
      if ($body.text().includes('Sorry, an unexpected problem occurred')) {
        cy.reload();
      }
    });

    // Ensure the home page is fully loaded
    cy.get('.top-banner', { timeout: 60000 }).should('be.visible');

    // Navigate to manifested orders
    cy.visit('https://business.parcel.royalmail.com/orders/manifested/');

    cy.intercept('POST', 'https://apibusiness.parcel.royalmail.com/api/manifested-orders/v2')
      .as('manifestedOrders');

    // Ensure the manifested orders page is fully loaded
    cy.url().should('eq', 'https://business.parcel.royalmail.com/orders/manifested/');
    cy.get('#search', { timeout: 60000 }).should('be.visible');
    cy.wait('@manifestedOrders').then(data => {
      cy.writeFile('interceptedRequest.json', data);
    });

    // Wait for the first row of orders to be visible
    cy.get('div[data-rowindex="0"]').should('be.visible');
  });
});
