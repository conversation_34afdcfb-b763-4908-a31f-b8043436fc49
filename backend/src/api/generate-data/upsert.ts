import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON>en, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { <PERSON> } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");

class GeneralDataCreate implements TypeAPIHandler {

  url = "/api/general-data/:type";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string(),
      name: Joi.string(),
      field1: Joi.string(),
      field2: Joi.string(),
      publicPermission: Joi.object({
        c: Joi.boolean(),
        r: Joi.boolean(),
        u: Joi.boolean(),
        d: Joi.boolean(),
      }),
      data: Joi.any(),
    }),
    params: Joi.object({
      type: Joi.string().required(),
    })
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    // UPDATE EXISTING
    if (request.body.id) {
      const find = await DB.GeneralData.findByPk(request.body.id);
      if (!!find) {
        if (find.userId !== request.user?.id && !find.publicPermission.u && request.user.role !== 'admin') {
          throw new Error(ERROR.PERMISSION_DENIED);
        }
        for (let key in request.body) {
          if (key === 'id' || key === 'userId') continue;
          find[key] = request.body[key];
        }
        await find.save();
        return { success: true, data: find };
      }
    }
    // CREATE NEW
    const data = await DB.GeneralData.create({
      id: VarHelper.genId(),
      ...request.body,
      type: request.params.type,
      userId: request.user?.id,
    });

    return {
      success: true,
      data,
    }
  };
}

export default new GeneralDataCreate();