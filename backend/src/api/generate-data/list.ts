import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAuthenOptional, combineMiddlewares } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");

class GeneralDataList implements TypeAPIHandler {

  url = "/api/general-data/:type";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      userId: Joi.string().allow(''),
      name: Joi.string(),
      field1: Joi.string(),
      field2: Joi.string(),
      orderBy: Joi.string(),
    }),
    params: Joi.object({
      type: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    checkAuthenOptional,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { userId, name, field1, field2, orderBy } = request.query;
    const list = await DB.GeneralData.findAll({
      where: VarHelper.removeUndefinedField({
        userId: userId  === 'all' ? undefined : userId,
        type: request.params.type,
        name,
        field1,
        field2,
      }),
      order: [
        [orderBy || 'updatedAt', 'DESC'],
      ],
    });
    const filteredList = list.filter(val => {
      if (request.user?.role === 'admin') return true;
      if (val.userId !== request.user?.id && !val.publicPermission.r) return false;
      return true;
    });

    return {
      success: true,
      data: filteredList,
    }
  };
}

export default new GeneralDataList();
