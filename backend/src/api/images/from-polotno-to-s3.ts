import { TypeAP<PERSON>Hand<PERSON> } from 'type';
import { validateRequest, combineMiddlewares, receiveFileAndFields, IModifiedBody, TFileField } from '../api-middlewares'
import { ERROR } from 'const';
import { DB, InMemory as RedisCache } from 'db';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'helpers';
import Joi = require("joi");


class FromPolotno implements TypeAPIHandler {
  url = '/api/images/from-polotno-to-s3';
  method = 'POST';
  apiSchema = {
    body: Joi.object({
      json: Joi.any(),
      name: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request, reply) => {
    const { json, name } = request.body;
    const s3Url = await Polotno.jsonToS3(json, `polotno-image/${name}-${new Date().getTime()}.png`);

    return {
      success: true,
      data: s3Url,
    }

  }
    
}

export default new FromPolotno();
