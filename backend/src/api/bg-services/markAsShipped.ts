import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON>elper } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import { generatePdf } from '../pdf/generatePdf';

const moment = require('moment');

class NewOrder implements TypeAPIHandler {

  url = "/api/bg-services/:id/mark-as-shipped";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      printJobId: Joi.string().required(),
      orderId: Joi.string().required(),
      resellerId: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;

    if (user.role !== 'admin') throw new Error(ERROR.PERMISSION_DENIED);

    const reseller = await DB.User.findByPk(body.resellerId);
    if (!reseller) throw new Error(ERROR.NOT_EXISTED);
    const bgServices = reseller.otherData?.bgServices || {};

    (async () => {
      if (bgServices.webhookUrl) {
        fetch(bgServices.webhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...bgServices.authenHeaders,
          },
          body: JSON.stringify({
            orderId: body.orderId,
            status: 'shipped',
            timestamp: moment().format(),
          }),
        })
      }
    })();

    return {
      success: true,
    }
  };
}

export default new NewOrder();