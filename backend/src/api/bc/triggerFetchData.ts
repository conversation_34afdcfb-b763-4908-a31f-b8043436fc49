import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAd<PERSON>, check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import { Op } from "sequelize";

class TriggerFetchData implements TypeAPIHandler {

  url = "/api/bc/trigger-fetch-data";
  method = "POST";
  apiSchema = {
    body: Joi.object({
        postingDateFrom: Joi.string().required(),
        postingDateTo: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request: TRequestUser, reply) => {
    const headers = request.headers;
    if (headers['x-api-key'] !== 'kgmdqtveva') {
      return reply.status(401).send({
        success: false,
        message: 'Unauthorized',
      });
    }

    const { postingDateFrom, postingDateTo } = request.body;

    // check if postingDateFrom and postingDateTo is yyyy-mm-dd
    if (!postingDateFrom.match(/^\d{4}-\d{2}-\d{2}$/) || !postingDateTo.match(/^\d{4}-\d{2}-\d{2}$/)) {
      return reply.status(400).send({
        success: false,
        message: 'Invalid posting date format, should be yyyy-mm-dd',
      });
    }
    const saleHeaders = await BCHelper.getAL('/mySalesInvoices', `$filter=postingDate ge ${postingDateFrom} and postingDate le ${postingDateTo}`);
    const saleLines = await BCHelper.getAL('/mySalesInvoiceLines', `$filter=postingDate ge ${postingDateFrom} and postingDate le ${postingDateTo}`);
    await DB.BCSaleInvoiceHeader.bulkCreate(saleHeaders, { updateOnDuplicate: ['@odata.etag'] });
    await DB.BCSaleInvoiceLine.bulkCreate(saleLines, { updateOnDuplicate: ['@odata.etag'] });

    return {
      success: true,
      data: {
        saleHeaders: `records found: ${saleHeaders.length}`,
        saleLines: `records found: ${saleLines.length}`,
      }
    }
  };
}

export default new TriggerFetchData();
