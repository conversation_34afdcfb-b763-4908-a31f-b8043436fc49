import { 
    transformSalesInvoiceHeader, 
    transformSalesInvoiceLine, 
    transformSalesInvoiceHeaders, 
    transformSalesInvoiceLines 
} from '../fieldMapping';

// Test data based on the actual input files
const testHeaderData = {
    "@odata.etag": "W/\"JzE5OzYwNDI5Nzk2OTkyNDMzNjg3NTgxOzAwOyc=\"",
    "No": "IN229220",
    "SelltoCustomerNo": "EGRE290",
    "BilltoCustomerNo": "EGRE290",
    "BilltoName": "Greenzone",
    "BilltoAddress": "Cleaning & Support Services Ltd",
    "BilltoAddress2": "Unit 66 Spaces Business Centre",
    "BilltoCity": "15-17 Ingate Place",
    "ShiptoName": "Greenzone",
    "ShiptoAddress": "Cleaning & Support Services Ltd",
    "ShiptoCity": "15-17 Ingate Place",
    "PostingDate": "2025-01-28",
    "SelltoCustomerName2": "",
    "SelltoAddress": "Cleaning & Support Services Ltd",
    "SelltoAddress2": "Unit 66 Spaces Business Centre",
    "SelltoCity": "15-17 Ingate Place",
    "SelltoContact": "",
    "BilltoPostCode": "SW8 3NS",
    "BilltoCounty": "London",
    "BilltoCountryRegionCode": "GB",
    "SelltoPostCode": "SW8 3NS",
    "SelltoCounty": "London",
    "SelltoCountryRegionCode": "GB",
    "ExternalDocumentNo": "PAUL",
    "createdAt": "2025-08-20T03:36:12.022Z",
    "updatedAt": "2025-08-20T03:36:12.022Z"
};

const testLineData = {
    "@odata.etag": "W/\"JzE5OzI3MTA0NjUwODcwMjEyNjg3MTkxOzAwOyc=\"",
    "DocumentNo": "IN238472",
    "LineNo": 10000,
    "No": "KRU062",
    "Description": "Jangro White Centre feed Roll 2ply 150m AF114",
    "UnitofMeasure": "",
    "Quantity": 1,
    "UnitPrice": 12.68,
    "VATPct": 20,
    "AmountIncludingVAT": 15.22,
    "VATCalculationType": "Normal_x0020_VAT",
    "VATClauseCode": "",
    "VATBusPostingGroup": "DOMESTIC",
    "VATProdPostingGroup": "VAT20",
    "VATBaseAmount": 12.68,
    "LineAmount": 12.68,
    "VATDifference": 0,
    "VATIdentifier": "VAT20",
    "PostingDate": "2025-03-27",
    "createdAt": "2025-08-20T03:36:32.840Z",
    "updatedAt": "2025-08-20T03:36:32.840Z"
};

describe('Field Mapping Functions', () => {
    describe('transformSalesInvoiceHeader', () => {
        it('should transform header data correctly', () => {
            const result = transformSalesInvoiceHeader(testHeaderData);
            
            // Check that mapped fields are transformed
            expect(result['No.']).toBe('IN229220');
            expect(result['Sell-to Customer No.']).toBe('EGRE290');
            expect(result['Bill-to Customer No.']).toBe('EGRE290');
            expect(result['Bill-to Name']).toBe('Greenzone');
            expect(result['Bill-to Address']).toBe('Cleaning & Support Services Ltd');
            expect(result['Bill-to Address 2']).toBe('Unit 66 Spaces Business Centre');
            expect(result['Bill-to City']).toBe('15-17 Ingate Place');
            expect(result['Ship-to Name']).toBe('Greenzone');
            expect(result['Ship-to Address']).toBe('Cleaning & Support Services Ltd');
            expect(result['Ship-to City']).toBe('15-17 Ingate Place');
            expect(result['Posting Date']).toBe('2025-01-28');
            expect(result['Sell-to Customer Name 2']).toBe('');
            expect(result['Sell-to Address']).toBe('Cleaning & Support Services Ltd');
            expect(result['Sell-to Address 2']).toBe('Unit 66 Spaces Business Centre');
            expect(result['Sell-to City']).toBe('15-17 Ingate Place');
            expect(result['Sell-to Contact']).toBe('');
            expect(result['Bill-to Postcode']).toBe('SW8 3NS');
            expect(result['Bill-to County']).toBe('London');
            expect(result['Bill-to Country/Region Code']).toBe('GB');
            expect(result['Sell-to Postcode']).toBe('SW8 3NS');
            expect(result['Sell-to County']).toBe('London');
            expect(result['Sell-to Country/Region Code']).toBe('GB');
            expect(result['External Document No.']).toBe('PAUL');
            
            // Check that metadata fields are excluded
            expect(result['@odata.etag']).toBeUndefined();
            expect(result['createdAt']).toBeUndefined();
            expect(result['updatedAt']).toBeUndefined();
            
            // Check that the result is not empty
            expect(Object.keys(result).length).toBeGreaterThan(0);
        });
    });

    describe('transformSalesInvoiceLine', () => {
        it('should transform line data correctly', () => {
            const result = transformSalesInvoiceLine(testLineData);
            
            // Check that mapped fields are transformed
            expect(result['Document No.']).toBe('IN238472');
            expect(result['Line No.']).toBe(10000);
            expect(result['No.']).toBe('KRU062');
            expect(result['Description']).toBe('Jangro White Centre feed Roll 2ply 150m AF114');
            expect(result['Unit of Measure']).toBe('');
            expect(result['Quantity']).toBe(1);
            expect(result['Unit Price']).toBe(12.68);
            expect(result['VAT %']).toBe(20);
            expect(result['Amount Including VAT']).toBe(15.22);
            expect(result['VAT Calculation Type']).toBe('Normal_x0020_VAT');
            expect(result['VAT Clause Code']).toBe('');
            expect(result['VAT Bus. Posting Group']).toBe('DOMESTIC');
            expect(result['VAT Prod. Posting Group']).toBe('VAT20');
            expect(result['VAT Base Amount']).toBe(12.68);
            expect(result['Line Amount']).toBe(12.68);
            expect(result['VAT Difference']).toBe(0);
            expect(result['VAT Identifier']).toBe('VAT20');
            expect(result['Posting Date']).toBe('2025-03-27');
            
            // Check that metadata fields are excluded
            expect(result['@odata.etag']).toBeUndefined();
            expect(result['createdAt']).toBeUndefined();
            expect(result['updatedAt']).toBeUndefined();
            
            // Check that the result is not empty
            expect(Object.keys(result).length).toBeGreaterThan(0);
        });
    });

    describe('transformSalesInvoiceHeaders', () => {
        it('should transform array of headers correctly', () => {
            const inputArray = [testHeaderData, { ...testHeaderData, "No": "IN238472" }];
            const result = transformSalesInvoiceHeaders(inputArray);
            
            expect(Array.isArray(result)).toBe(true);
            expect(result.length).toBe(2);
            expect(result[0]['No.']).toBe('IN229220');
            expect(result[1]['No.']).toBe('IN238472');
        });

        it('should throw error for non-array input', () => {
            expect(() => transformSalesInvoiceHeaders(testHeaderData as any)).toThrow('Input must be an array');
        });
    });

    describe('transformSalesInvoiceLines', () => {
        it('should transform array of lines correctly', () => {
            const inputArray = [testLineData, { ...testLineData, "DocumentNo": "IN241442", "LineNo": 20000 }];
            const result = transformSalesInvoiceLines(inputArray);
            
            expect(Array.isArray(result)).toBe(true);
            expect(result.length).toBe(2);
            expect(result[0]['Document No.']).toBe('IN238472');
            expect(result[1]['Document No.']).toBe('IN241442');
            expect(result[0]['Line No.']).toBe(10000);
            expect(result[1]['Line No.']).toBe(20000);
        });

        it('should throw error for non-array input', () => {
            expect(() => transformSalesInvoiceLines(testLineData as any)).toThrow('Input must be an array');
        });
    });

    describe('Edge cases', () => {
        it('should handle empty objects', () => {
            const result = transformSalesInvoiceHeader({});
            expect(result).toEqual({});
        });

        it('should handle objects with only metadata fields', () => {
            const result = transformSalesInvoiceHeader({
                "@odata.etag": "test",
                "createdAt": "2025-01-01",
                "updatedAt": "2025-01-01"
            });
            expect(result).toEqual({});
        });

        it('should handle objects with unmapped fields', () => {
            const result = transformSalesInvoiceHeader({
                "CustomField": "CustomValue",
                "No": "IN123"
            });
            expect(result['CustomField']).toBe('CustomValue');
            expect(result['No.']).toBe('IN123');
        });
    });
});
