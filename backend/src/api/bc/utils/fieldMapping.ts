/**
 * Transform API response from Business Central API format to desired format
 * This file maps camelCase field names back to the original Business Central field names
 */

// Field mapping from API response (PascalCase) to desired format for Sales Invoice Header
const fieldMappingHeader = {
    "No": "No.",
    "SelltoCustomerNo": "Sell-to Customer No.",
    "BilltoCustomerNo": "Bill-to Customer No.",
    "BilltoName": "Bill-to Name",
    "BilltoAddress": "Bill-to Address",
    "BilltoAddress2": "Bill-to Address 2",
    "BilltoCity": "Bill-to City",
    "ShiptoName": "Ship-to Name",
    "ShiptoAddress": "Ship-to Address",
    "ShiptoCity": "Ship-to City",
    "PostingDate": "Posting Date",
    "SelltoCustomerName2": "Sell-to Customer Name 2",
    "SelltoAddress": "Sell-to Address",
    "SelltoAddress2": "Sell-to Address 2",
    "SelltoCity": "Sell-to City",
    "SelltoContact": "Sell-to Contact",
    "BilltoPostCode": "Bill-to Postcode",
    "BilltoCounty": "Bill-to County",
    "BilltoCountryRegionCode": "Bill-to Country/Region Code",
    "SelltoPostCode": "Sell-to Postcode",
    "SelltoCounty": "Sell-to County",
    "SelltoCountryRegionCode": "Sell-to Country/Region Code",
    "ExternalDocumentNo": "External Document No."
};

// Field mapping from API response (PascalCase) to desired format for Sales Invoice Lines
const fieldMappingLines = {
    "DocumentNo": "Document No.",
    "LineNo": "Line No.",
    "No": "No.",
    "Description": "Description",
    "UnitofMeasure": "Unit of Measure",
    "Quantity": "Quantity",
    "UnitPrice": "Unit Price",
    "VATPct": "VAT %",
    "AmountIncludingVAT": "Amount Including VAT",
    "VATCalculationType": "VAT Calculation Type",
    "VATClauseCode": "VAT Clause Code",
    "VATBusPostingGroup": "VAT Bus. Posting Group",
    "VATProdPostingGroup": "VAT Prod. Posting Group",
    "VATBaseAmount": "VAT Base Amount",
    "LineAmount": "Line Amount",
    "VATDifference": "VAT Difference",
    "VATIdentifier": "VAT Identifier",
    "PostingDate": "Posting Date"
};

export function transformSalesInvoiceHeader(data: any): any {
    const transformed: any = {};
    
    // Transform each field using the header mapping
    for (const key in data) {
        if (fieldMappingHeader[key]) {
            transformed[fieldMappingHeader[key]] = data[key];
        } else if (key !== "@odata.etag" && key !== "createdAt" && key !== "updatedAt") {
            // Include unmapped fields (except metadata fields)
            transformed[key] = data[key];
        }
    }
    
    return transformed;
}

export function transformSalesInvoiceLine(data: any): any {
    const transformed: any = {};
    
    // Transform each field using the lines mapping
    for (const key in data) {
        if (fieldMappingLines[key]) {
            transformed[fieldMappingLines[key]] = data[key];
        } else if (key !== "@odata.etag" && key !== "createdAt" && key !== "updatedAt") {
            // Include unmapped fields (except metadata fields)
            transformed[key] = data[key];
        }
    }
    
    return transformed;
}

export function transformSalesInvoiceLines(data: any[]): any[] {
    if (!Array.isArray(data)) {
        throw new Error('Input must be an array');
    }
    
    return data.map(line => transformSalesInvoiceLine(line));
}

export function transformSalesInvoiceHeaders(data: any[]): any[] {
    if (!Array.isArray(data)) {
        throw new Error('Input must be an array');
    }
    
    return data.map(header => transformSalesInvoiceHeader(header));
}