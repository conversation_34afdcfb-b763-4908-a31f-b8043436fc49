import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAdmin, checkA<PERSON>en, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import { Op } from "sequelize";
import * as XLSX from 'xlsx';
import { transformSalesInvoiceHeaders, transformSalesInvoiceLines } from './utils/fieldMapping';
import * as moment from 'moment';
import * as fs from 'fs';

const DEBUG = false

class GenSIRReport implements TypeAPIHandler {

    url = "/api/bc/gen-sir-report";
    method = "POST";
    apiSchema = {
        body: Joi.object({
            client: Joi.string().required(),
            postingDateFrom: Joi.string().required(),
            postingDateTo: Joi.string().required(),
        }),
    }

    preHandler = combineMiddlewares([
        validateRequest(this.apiSchema),
    ]);

    handler = async (request: TRequestUser, reply) => {
        const headers = request.headers;
        if (headers['x-api-key'] !== 'kgmdqtveva') {
            return reply.status(401).send({
                success: false,
                message: 'Unauthorized',
            });
        }

        const { client, postingDateFrom, postingDateTo } = request.body;

        const saleHeaders = await DB.BCSaleInvoiceHeader.findAll({
            where: {
                SelltoCustomerNo: client,
                PostingDate: {
                    [Op.between]: [postingDateFrom, postingDateTo],
                },
            },
            raw: true,
            logging: console.log,
        });
        DEBUG && fs.writeFileSync(__dirname + '/saleHeaders.json', JSON.stringify(saleHeaders, null, 2));

        const saleLines = await DB.BCSaleInvoiceLine.findAll({
            where: {
                DocumentNo: {
                    [Op.in]: saleHeaders.map(header => header.No),
                },
            },
            raw: true,
        });
        DEBUG && fs.writeFileSync(__dirname + '/saleLines.json', JSON.stringify(saleLines, null, 2));

        const greenZoneClientNo = client; // EGRE290
        const saleInvoiceHeader = transformSalesInvoiceHeaders(saleHeaders);
        DEBUG && fs.writeFileSync(__dirname + '/saleInvoiceHeader.json', JSON.stringify(saleInvoiceHeader, null, 2));
        const saleInvoiceLine = transformSalesInvoiceLines(saleLines);
        DEBUG && fs.writeFileSync(__dirname + '/saleInvoiceLine.json', JSON.stringify(saleInvoiceLine, null, 2));
        const contractLines = require('./utils/contract_line.json');
        const wb = XLSX.utils.book_new();
        const wsData = [];
        wsData.push([
            'PI',
            'MSC',
            'Nominal A/C Ref',
            'Date',
            'Reference',
            'Details',
            'Quantity',
            'Product Details',
            'Net Amount',
            'Tax Code',
            'Amount Including VAT',
            'Tax Amount',
            'External Document No.',
            'Extra Ref',
            'DeliveryAddress/Name 1',
            'Delivery Address 2',
            'Sales Invoice Header - BRS0011WebOrderNo',
            'BRS011ItemType',
            'VAT Identifier',
        ]);
        const indexes = {
            PI: 0,
            MSC: 1,
            'Nominal A/C Ref': 2,
            Date: 3,
            Reference: 4,
            Details: 5,
            Quantity: 6,
            'Product Details': 7,
            'Net Amount': 8,
            'Tax Code': 9,
            'Amount Including VAT': 10,
            'Tax Amount': 11,
            'External Document No.': 12,
            'Extra Ref': 13,
            'DeliveryAddress/Name 1': 14,
            'Delivery Address 2': 15,
            'Sales Invoice Header - BRS0011WebOrderNo': 16,
            'BRS011ItemType': 17,
            'VAT Identifier': 18,
        };


        saleInvoiceHeader.forEach(header => {
            if (header['Bill-to Customer No.'] !== greenZoneClientNo) return;
            const row = [];
            row[indexes.PI] = 'MSC';
            row[indexes.MSC] = 'PI';
            // row[indexes['VAT Identifier']] = 'VAT20';
            row[indexes['Reference']] = header['No.'];
            row[indexes['Date']] = moment(header['Posting Date'], 'YYYY-MM-DD').format('DD/MM/YYYY');
            const items = saleInvoiceLine.filter(line => line['Document No.'] === header['No.']);
            items.forEach(item => {
                const sirRow = row.slice();
                sirRow[indexes['Details']] = item['No.'];
                const findContractLine = contractLines.find(cl => cl['No.'] === item['No.'] && cl['Bill-to Customer'] === header['Bill-to Customer No.']);
                if (findContractLine) {
                    sirRow[indexes['BRS011ItemType']] = findContractLine['Item Type'];
                    sirRow[indexes['Nominal A/C Ref']] = findContractLine['Item Type'] === 'Consumable' ? '5011' : '6900';
                    const quantity = +item['Quantity'];
                    if (!quantity) return;
                    sirRow[indexes['Quantity']] = quantity;
                    sirRow[indexes['Product Details']] = item['Description'];
                    sirRow[indexes['Net Amount']] = item['Line Amount'];
                    sirRow[indexes['Tax Code']] = '1';
                    const vatPercent = +item['VAT %'] / 100;
                    sirRow[indexes['VAT Identifier']] = item['VAT Prod. Posting Group'];
                    sirRow[indexes['Amount Including VAT']] = +item['Line Amount'] * quantity * (1 + vatPercent);
                    sirRow[indexes['Tax Amount']] = +item['Line Amount'] * quantity * vatPercent;
                    // excel formula =MID([@[DeliveryAddress/Name 1]],11,4)&IF([@[Sales Invoice Header - BRS0011WebOrderNo]]<>"","/"&[@[Sales Invoice Header - BRS0011WebOrderNo]],"")
                    const deliveryName = header['Ship-to Name'] || '';
                    const webOrderNo = '';

                    // Extract 4 characters starting from position 11 (0-indexed, so position 10)
                    const midPart = deliveryName.length >= 14 ? deliveryName.substring(10, 14) : '';

                    // If webOrderNo is not empty, append "/" + webOrderNo, otherwise just the mid part
                    const extraRef = midPart + (webOrderNo ? '/' + webOrderNo : '');

                    sirRow[indexes['Extra Ref']] = extraRef;

                    sirRow[indexes['DeliveryAddress/Name 1']] = header['Ship-to Name'];
                    sirRow[indexes['Delivery Address 2']] = header['Ship-to Address'];
                    sirRow[indexes['External Document No.']] = header['External Document No.'];
                    wsData.push(sirRow);
                }
            })
        });
        const ws = XLSX.utils.aoa_to_sheet(wsData);
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');


        // send file to client
        const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });
        reply.header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        reply.header('Content-Disposition', `attachment; filename=sir-report-${client}-${postingDateFrom}-${postingDateTo}.xlsx`);
        reply.send(buffer);
    };
}

export default new GenSIRReport();
