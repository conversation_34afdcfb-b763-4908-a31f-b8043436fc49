import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TDesign } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { AWSHelper, Shopify, VarHelper } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import { Op } from "sequelize";
import RolesHelper from "../../helpers/RolesHelper";
import { DesignModel } from "db/Schema.Design";

const updateShopifyProduct = async (design: DesignModel, params: any) => {
  const productLibrary = await DB.Product.findByPk(design.productId);
  const products = design.products.slice() || [];
  await Promise.all(products.map(async product => {
    const productId = product.productId;
    try {
      const store = await DB.OnlineStore.findOne({
        where: {
          id: product.storeId,
          inactive: {
            [Op.not]: true
          }
        }
      });
      if (!store) return;
      const jsonRes = await Shopify.apiCall(
        `${store.url}/admin/api/2023-04/products/${productId}.json`,
        'put',
        store.data?.shopifyAccessToken,
        {
          product: {
            title: params.name,
            body_html: params.description || design.description,
          }
        }
      );
      let { product: shopifyProduct } = jsonRes;
      console.log('shopifyProduct', shopifyProduct);
      if (!shopifyProduct) return;
      const { variants: shopifyVariants, images } = shopifyProduct;
      if (!design.variants || design.variants.length === 0) {
        console.log('ONLY UPDATE IMAGES');
        // only update images
        for (let i=0; i<images.length; i++) {
          await Shopify.apiCall(
            `${store.url}/admin/api/2023-04/products/${productId}/images/${images[i].id}.json`,
            'delete',
            store.data?.shopifyAccessToken,
          );
        }
        // push design.image and design.galleries to shopify
        const toBeUploadedImages = [design.image, ...(design.galleries || [])];
        for (let i=0; i<toBeUploadedImages.length; i++) {
          await Shopify.uploadImageToShopifyStore(toBeUploadedImages[i], {
            storeUrl: store.url,
            token: store.data?.shopifyAccessToken,
            productId,
          });
        }
        return;
      } 
      // sync variants
      console.log('SYNC VARIANTS...');
      const newMatchImage = {};
      const toBeDeletedVariants = [];
      for (let i=0; i<shopifyVariants.length; i++) {
        const variant = shopifyVariants[i];
        const find = product.matchDesignId[String(variant.id)];
        if (!find) {
          toBeDeletedVariants.push(variant.id);
        }
      }
      console.log('TO BE DELETED VARIANTS', toBeDeletedVariants)
      // delete variants
      for (let i=0; i<toBeDeletedVariants.length; i++) {
        await Shopify.apiCall(
          `${store.url}/admin/api/2023-04/products/${productId}/variants/${toBeDeletedVariants[i]}.json`,
          'delete',
          store.data?.shopifyAccessToken,
        );
      }
      const envTag = !process.env.BACKEND_CMS_URL ? '' : (
        process.env.BACKEND_CMS_URL.includes('dev.bg-production') ? '-dev' : ''
      );
      // loop through design variants and add to shopify
      for (let i=0; i<design.variants.length; i++) {
        const bgVariant = design.variants[i];
        const find = Object.keys(product.matchDesignId).find(shopifyVId => product.matchDesignId[shopifyVId] === bgVariant.variantDesignId);
        if (!find) {
          // upload image
          const { image } = await Shopify.uploadImageToShopifyStore(bgVariant.image, {
            storeUrl: store.url,
            token: store.data?.shopifyAccessToken,
            productId,
          });
          console.log('CREATRE NEW VARIANT');
          // create new variant
          const variantApiCallData = await Shopify.apiCall(
            `${store.url}/admin/api/2023-04/products/${productId}/variants.json`,
            'post',
            store.data?.shopifyAccessToken,
            {
              variant: {
                option1: bgVariant.style,
                price: String((Number(bgVariant.price) || 0).toFixed(2)),
                sku: `d${envTag}-${design.id}-${bgVariant.variantDesignId}`,
                fulfillment_service: 'msc-fulfillment',
                image_id: image.id,
                inventory_management: null,
              }
            }
          );
          product.matchImageId[image.id] = bgVariant.image;
          product.matchDesignId[String(variantApiCallData.variant.id)] = bgVariant.variantDesignId;
          newMatchImage[image.id] = bgVariant.image;
          // update inventory
          const inventoryId = variantApiCallData.variant.inventory_item_id;
          if (inventoryId && productLibrary) {
            await Shopify.apiCall(`${store.url}/admin/api/2023-04/inventory_items/${inventoryId}.json`, 'put', store.data?.shopifyAccessToken, {
              "inventory_item":{
                "id": inventoryId,
                "cost": String((Number(productLibrary?.price) || 0).toFixed(2)),
              }
            });
          }
        } else {
          // check if image exists
          const findImageId = Object.keys(product.matchImageId).find(shopifyImageId => product.matchImageId[shopifyImageId] === bgVariant.image);
          let image_id = undefined;
          if (!findImageId) {
            const { image } = await Shopify.uploadImageToShopifyStore(bgVariant.image, {
              storeUrl: store.url,
              token: store.data?.shopifyAccessToken,
              productId,
            });
            image_id = image.id;
            newMatchImage[image.id] = bgVariant.image;
          } else {
            newMatchImage[findImageId] = bgVariant.image;
          }
          // update variant
          const variantApiCallData = await Shopify.apiCall(
            `${store.url}/admin/api/2023-04/products/${productId}/variants/${find}.json`,
            'put',
            store.data?.shopifyAccessToken,
            {
              variant: {
                option1: bgVariant.style,
                price: String((Number(bgVariant.price) || 0).toFixed(2)),
                sku: `d${envTag}-${design.id}-${bgVariant.variantDesignId}`,
                fulfillment_service: 'msc-fulfillment',
                image_id,
                inventory_management: null,
              }
            }
          );
        }
      }
      product.matchImageId = newMatchImage;
      
      // refetch shopify product to remove unneeded image
      const jsonRes2 = await Shopify.apiCall(
        `${store.url}/admin/api/2023-04/products/${productId}.json`,
        'get',
        store.data?.shopifyAccessToken,
      );
      shopifyProduct = jsonRes2.product;
      const toBeDeletesImages = [];
      // loop through shopifyProduct and delete images that are not in matchImageId
      for (let i=0; i<shopifyProduct.images.length; i++) {
        const imageId = shopifyProduct.images[i].id;
        if (!product.matchImageId[imageId]) {
          toBeDeletesImages.push(imageId);
        }
      }
      for (let i=0; i<toBeDeletesImages.length; i++) {
        await Shopify.apiCall(
          `${store.url}/admin/api/2023-04/products/${productId}/images/${toBeDeletesImages[i]}.json`,
          'delete',
          store.data?.shopifyAccessToken,
        );
      }
    } catch (err) {
      console.log("updateShopifyProduct_err", err);
    }
  }));
  design.products = products;
  await design.save();
}

const checkImageQuality = async (design: TDesign, image: string) => {
  try {
    const quality: any = await AWSHelper.detectModerationLabel(image);
    const find = await DB.Design.findByPk(design.id);
    find.otherData = {
      ...(find.otherData || {}),
      imageQuality: quality,
    }
    await find.save();
    console.log("checkImageQuality", quality);
  } catch (error) {
    console.log("checkImageQuality_error", error);
  }
}

class UpsertDesign implements TypeAPIHandler {

  url = "/api/designs";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string(),
      productId: Joi.string(),
      designId: Joi.string(),
      name: Joi.string(),
      description: Joi.string(),
      image: Joi.string(),
      galleries: Joi.array().items(Joi.string()),
      width: Joi.number(),
      height: Joi.number(),
      editorWidth: Joi.number(),
      editorHeight: Joi.number(),
      printAreas: Joi.array().items(Joi.object({
        width: Joi.number(),
        height: Joi.number(),
        top: Joi.number(),
        left: Joi.number(),
      })),
      availableForResellerIds: Joi.object(),

      isCustomizable: Joi.boolean(),
      products: Joi.any(),
      data: Joi.any(),
      otherData: Joi.any(),
      wholeSale: Joi.boolean(),
      printOnDemand: Joi.boolean(),
      customProduct: Joi.boolean(),
      resalePrice: Joi.number(),
      updateShopify: Joi.boolean(),
      brands: Joi.array().items(Joi.object({
        storeId: Joi.string(),
        name: Joi.string(),
      })),
      parentDesignId: Joi.string().allow(''),
      variants: Joi.array().items(Joi.object({
        style: Joi.string(),
        variantDesignId: Joi.string(),
        image: Joi.string(),
        galleries: Joi.array().items(Joi.string()),
        price: Joi.number(),
      })),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;
    const { updateShopify, ...params } = body;
    if (RolesHelper.isNoRoles(user)) throw new Error(ERROR.PERMISSION_DENIED);
    // UPDATE EXISTING
    if (params.id) {
      const find = await DB.Design.findByPk(params.id);
      if (!!find) {
        if (find.createdByUserId !== RolesHelper.getResellerId(user) && RolesHelper.isUserOrReseller(user)) throw new Error(ERROR.PERMISSION_DENIED);
        if (params.image !== find.image) {
          checkImageQuality(find, params.image);
        }
        for (let key in params) {
          find[key] = params[key];
        }
        await find.save();
        if (updateShopify) {
          await updateShopifyProduct(find, params);
        }
        return { success: true, data: find };
      }
    }

    const design = {
      id: VarHelper.genId(),
      ...params,
      // only admin and reseller
      // if a member of reseller (role user), still save as reseller
      createdByUserId: user.resellerId || user.id,
      createdByUserType: user.resellerId ? 'reseller' : user.role,
    };
    if (design.image) {
      checkImageQuality(design, design.image);
    }

    // const candleTemplate = await genCandleTemplate({} as TDesign, design);
    // if (candleTemplate) {
    //   design.data = {
    //     ...design.data,
    //     candleTemplate,
    //   }
    // }
    // CREATE NEW
    const data = await DB.Design.create(design);

    return {
      success: true,
      data,
    }
  };
}

export default new UpsertDesign();
