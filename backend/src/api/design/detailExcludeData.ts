import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest, checkAuthenOptional } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON>ar<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";

class DetailDesign implements TypeAPIHandler {

  url = "/api/designs/:id/exclude-data";
  method = "GET";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthenOptional,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.params;
    // const user = request.user;
    // if (user.role !== 'admin' && user.role !== 'reseller') throw new Error(ERROR.PERMISSION_DENIED);

    const design = await DB.Design.findOne({
      where: {
        id,
      },
      attributes: {
        exclude: ['data'],
      },
    })
    if (!design) throw new Error(ERROR.NOT_EXISTED);
    // if (user.role === 'reseller' && design.createdByUserId !== user.id) {
    //   if (!design.availableForResellerIds?.all)
    //   throw new Error(ERROR.PERMISSION_DENIED);
    // }

    const user = await DB.User.findByPk(design.createdByUserId);
    design.setDataValue('createdByUserName', user.firstName + ' ' + user.lastName);

    return {
      success: true,
      data: design,
    }
  };
}

export default new DetailDesign();
