import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import Joi = require("joi");
import { ERROR } from "const";
import { Op, Sequelize } from 'sequelize';
import { checkReseller } from "api/api-middlewares/authen";

class ListDesigns implements TypeAPIHandler {

  url = "/api/designs";
  method = "GET";
  apiSchema = {
    query: Joi.object({
      page: Joi.number(),
      createdByUserId: Joi.string().allow(''),
      excludeAll: Joi.number(),
      wholeSale: Joi.number(),
      printOnDemand: Joi.number(),
      customProduct: Joi.number(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const user = request.user;
    if (user.role === 'guess') throw new Error(ERROR.PERMISSION_DENIED);
    let { page, createdByUserId, wholeSale, printOnDemand, customProduct, excludeAll } = request.query;

    if (user.role === 'reseller' && !createdByUserId) throw new Error(ERROR.PERMISSION_DENIED);
    if (user.role === 'reseller' && createdByUserId !== user.id) throw new Error(ERROR.PERMISSION_DENIED);
    if (user.role === 'user' && createdByUserId !== user.resellerId) throw new Error(ERROR.PERMISSION_DENIED);

    if (!page) page = 1;

    const productNewTypeQuery: any = {};
    if (wholeSale === '0' || wholeSale === '1') productNewTypeQuery.wholeSale = Boolean(wholeSale);
    if (printOnDemand === '0' || printOnDemand === '1') productNewTypeQuery.printOnDemand = Boolean(printOnDemand);
    if (customProduct === '0' || customProduct === '1') productNewTypeQuery.customProduct = Boolean(customProduct);

    const PAGE_SIZE = 1000;
    const excludeEmptyDesign = {
      data: {
        [Op.not]: null
      },
      inactive: {
        [Op.not]: true
      }
    }
    const excludeVariantDesign = {
      name: {
        [Op.ne]: null
      }
    }
    console.log('createdByUserId', createdByUserId);
    const result = await DB.Design.findAndCountAll({
      where: !!createdByUserId ? {
        [Op.or]: [
          { createdByUserId, },
          !!excludeAll ? undefined : { availableForResellerIds: { all: true } },
          { availableForResellerIds: { [createdByUserId]: true } },
        ].filter(Boolean),
        ...excludeEmptyDesign,
        ...productNewTypeQuery,
        ...excludeVariantDesign,
      } : {
        ...excludeEmptyDesign,
        ...productNewTypeQuery,
        ...excludeVariantDesign,
      },
      order: [
        ['updatedAt', 'DESC'],
      ],
      offset: (page - 1) * PAGE_SIZE,
      limit: PAGE_SIZE,
      attributes: {
        exclude: ['data'],
      },
      include: [{
        model: DB.Product,
        as: "product",
        attributes: ["data"]
      }]
    });

    return {
      success: true,
      data: {
        list: result.rows,
        total: result.count,
        hasNext: page * PAGE_SIZE < result.count,
      },
    }
  };
}

export default new ListDesigns();
