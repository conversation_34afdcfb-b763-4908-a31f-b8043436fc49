import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkAd<PERSON>, checkA<PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { VarHelper } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import { Op } from "sequelize";
import RolesHelper from "../../helpers/RolesHelper";

class GetPDFNames implements TypeAPIHandler {

  url = "/api/designs/get-pdf-names";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      items: Joi.array().items(Joi.object({
        url: Joi.string(),
        designId: Joi.string(),
        name: Joi.string(),
        lineId: Joi.number(),
        orderId: Joi.string(),
        orderNumber: Joi.number(),
        shopifyProductId: Joi.number(),
        quantity: Joi.number(),
      })),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkA<PERSON>en,
    checkAdmin,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;
    const { items } = body;
    
    const matchShopifyProductToDesigns = {};
    const matchDesignToProduct = {};

    for (let i=0; i<items.length; i++) {
      const { shopifyProductId, orderNumber, quantity, designId, orderId } = items[i];


      const design = await (async () => {
        if (!!designId) return await DB.Design.findByPk(designId);
        if (!shopifyProductId) return null;
        return matchShopifyProductToDesigns[shopifyProductId]
        ? matchShopifyProductToDesigns[shopifyProductId]
        : await DB.Design.findOne({
            where: {
              products: {
                [Op.like]: `%:${shopifyProductId},%`,
              }
            }
          });
      })();
      
      if (!design) continue;
      matchShopifyProductToDesigns[shopifyProductId] = design;
      const product = matchDesignToProduct[design.productId]
        ? matchDesignToProduct[design.productId]
        : await DB.Product.findByPk(design.productId);

      if (!product || !product.printerIdentificatorCode) continue;
      items[i].name = `${orderId}-${product.printerIdentificatorCode}-${quantity || 1}.pdf`;
    }

    return {
      success: true,
      data: items,
    }
  };
}

export default new GetPDFNames();
