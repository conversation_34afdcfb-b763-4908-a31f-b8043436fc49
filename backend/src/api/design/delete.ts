import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { combineMiddlewares, checkAuthen, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import Joi = require("joi");
import { ERROR } from "const";
import RolesHelper from "../../helpers/RolesHelper";

class DeleteProduct implements TypeAPIHandler {

  url = "/api/designs/:id";
  method = "DELETE";
  apiSchema = {
    params: Joi.object({
      id: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { id } = request.params;
    const user = request.user;
    if (RolesHelper.isNoRoles(user)) throw new Error(ERROR.PERMISSION_DENIED);

    const design = await DB.Design.findByPk(id);
    if (RolesHelper.isUserOrReseller(user) && RolesHelper.getResellerId(user) !== design.createdByUserId) {
      throw new Error(ERROR.PERMISSION_DENIED);
    }

    await design.destroy();

    return {
      success: true,
    }
  };
}

export default new DeleteProduct();
