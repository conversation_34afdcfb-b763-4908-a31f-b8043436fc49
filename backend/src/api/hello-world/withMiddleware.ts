import { <PERSON>AP<PERSON><PERSON>and<PERSON> } from "type";
import {
  combineMiddlewares,
  sampleAddData,
  sampleReject,
} from "../api-middlewares";

class WithMiddleware implements TypeAPIHandler {
  url = "/b/api/test/with-middleware";
  method = "GET";

  preHandler = combineMiddlewares([sampleAddData, sampleReject]);

  handler = async (request, reply) => {
    console.log(request.foo);
    return { hello: "world4", ip: request.headers["x-real-ip"] || "127.0.0.1" };
  };
}

export default new WithMiddleware();
