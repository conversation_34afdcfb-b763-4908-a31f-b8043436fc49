import XeroHelper from "helpers/XeroHelper";
import { <PERSON>AP<PERSON>Handler } from "type";

class HelloWorld implements TypeAPIHandler {
  url = "/b/api/test/xero";
  method = "POST";

  handler = async (request, reply) => {
    await XeroHelper.checkAuthen();
    const { func, params } = request.body;
    const results = !!func && !!params ? await XeroHelper[func](...params) : null;
    return {
      hello: "world5",
      ip: request.headers["x-real-ip"] || "127.0.0.1",
      params: request.query,
      token: XeroHelper.tokenSet,
      results: results,
    };
  };
}

export default new HelloWorld();
