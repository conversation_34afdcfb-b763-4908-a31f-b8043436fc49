import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "helpers";
import { ERROR, MM_TO_PIXEL } from 'const';
import Joi = require("joi");
import axios from 'axios';
import { generatePdf } from '../pdf/generatePdf';
import { TProduct } from 'type';
const sampleProduct = require('./sample-product.json');
import { AlexanderLettering, BigCaslon } from './partnerInWineFont';
import { Op } from 'sequelize';
const fs = require('fs');
import { MATCH_SHOPIFY_PRODUCTS, genArtworkUrl, genPreviewUrl } from './partner-in-wine/index';

interface IShopifyOrder {
  id: number,
  customer: {
    email: string,
    first_name: string,
    last_name: string,
    [other: string]: any,
  },
  line_items: Array<{
    id: string | number, // variant id
    product_id: number,
    properties: Array<{
      name: string,
      value: string,
    }>,
    quantity: number,
    name: string,
    sku: string,
  }>,
  [other: string]: any,
}

class ShopifyWebhook implements TypeAPIHandler {
  url = "/api/online-stores/great-harbour-gifts/shopify-webhook";
  method = "POST";

  preHandler = combineMiddlewares([
    // validateRequest(this.apiSchema),
  ]);

  handler = async (request: TRequestUser, reply) => {
    console.log('PARTNER IN WINE - RECEIVE SHOPIFY WEBHOOK', new Date().toISOString());
    // console.log(request.body);

    if (!request.body.dev)
      DB.GeneralData.create({
        id: VarHelper.genId(),
        type: 'logs',
        name: 'shopify-webhook-great-harbour-gifts',
        field1: request.params.id,
        field2: '',
        userId: '1',
        data: request.body,
        publicPermission: {
          c: false, r: false, u: false, d: false,
        }
      })

    const body: IShopifyOrder = request.body;
    const order = body;
    if (!body.id) throw new Error('Order id missing');

    const { customer, line_items } = body;

    (async() => {
      for (let i=0; i<line_items.length; i++) {
        const variant = line_items[i];
        const product = await DB.Product.findOne({
          where: {
            data: {
              [Op.like]: `%${variant.sku}%`,
            }
          },
        });
        if (!product || !product.data?.skuPDFs) continue;


        const variantSKUItem = product.data?.skuPDFs.find(v => v.sku === variant.sku);
        if (!variantSKUItem) continue;

        const printJob = await DB.PrintJob.create({
          id: VarHelper.genId(),
          quantity: variant.quantity,
          clientId: '578953627077',
          productName: `${variant.name} - ${variant.sku} - ${customer.last_name}`,
          productId: product.id,
          designId: '',
          productVariantionName: 'Var + No Mirr',
          previewUrl: '',
          artworkUrls: [],
          data: {
            orderId: order.id,
            product: {
              physicalWidth: product.physicalWidth,
              physicalHeight: product.physicalHeight,
              printAreas: product.printAreas,
            },
            customer: order.customer, // shopify object
            shipping_address: order.shipping_address, // shopify object
            skuPDF: variantSKUItem.url,
          },
          isPaid: false,
          isPrinted: false,
          isRePrinted: false,
          isPDFDownloaded: false,
        });

        console.log('printJob ID: ', printJob.id);
      }
    })();

    return { success: true };
  }
};

const fromProperty = (properties, label, defaultValue) => {
  const findItem = (properties || []).find(val => val.name === label);
  return !findItem ? defaultValue : findItem.value;
}

export default new ShopifyWebhook();