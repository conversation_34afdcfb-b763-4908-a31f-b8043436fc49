import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { VarHelper } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import axios from 'axios';
import { generatePdf } from '../pdf/generatePdf';
import { generateCustomArtwork } from "api/pdf/generatePdfFromCustomArtwork";

interface IWebhookData {
  id: number,
  note: string,
}

interface IShopifyOrder {
  id: number,
  customer: {
    email: string,
    first_name: string,
    last_name: string,
    [other: string]: any,
  },
  line_items: Array<{
    id: string, // variant id
    properties: Array<{
      name: string,
      value: string,
    }>,
    quantity: number,
    name: string,
  }>,
  [other: string]: any,
}

class ShopifyWebhook implements TypeAPIHandler {
  url = "/api/online-stores/:id/shopify-webhook";
  method = "POST";

  preHandler = combineMiddlewares([
    // validateRequest(this.apiSchema),
  ]);

  handler = async (request: TRequestUser, reply) => {
    console.log('RECEIVE SHOPIFY WEBHOOK', new Date().toISOString());
    console.log(request.body);

    DB.GeneralData.create({
      id: VarHelper.genId(),
      type: 'logs',
      name: 'shopify-webhook',
      field1: request.params.id,
      field2: '',
      userId: '1',
      data: request.body,
      publicPermission: {
        c: false, r: false, u: false, d: false,
      }
    })

    const body: IWebhookData = request.body;
    if (!body.id) throw new Error('Order id missing');

    const store = await DB.OnlineStore.findByPk(request.params.id);
    if (!store) throw new Error('Store not existed');

    const url = store.url;
    const token = store.data?.shopifyAccessToken;
    const axiosRes: any = await axios.request({
      url: `${url}/admin/api/2023-04/orders/${body.id}.json`,
      method: 'get',
      headers: {
        'X-Shopify-Access-Token': token,
      },
    });
    const order: IShopifyOrder = axiosRes.data.order;
    if (!order) throw new Error('get Order Detail failed');

    const { customer, line_items } = order;

    for (let i = 0; i < line_items.length; i++) {
      const variant = line_items[i];
      const findPrintJob = variant.properties.find(val => val.name === 'Print Job');
      const findCustomArtwork = variant.properties.find(val => val.name === 'Custom Artwork');
      const findDesignID = variant.properties.find(val => val.name === 'Design ID');
      if (!findPrintJob && !findCustomArtwork) continue;
      if (!!findPrintJob) {
        (async () => {
          const printJobId = findPrintJob.value;
          const printJob = await DB.PrintJob.findByPk(printJobId);
          if (!printJob) return;
          const design = await DB.Design.findByPk(printJob.designId);
          if (!design) return;
          printJob.quantity = variant.quantity;
          printJob.clientId = store.resellerId;
          printJob.productName = `${design.name} - ${customer.last_name}`;
          printJob.data.orderId = order.id;
          printJob.data.customer = order.customer; // shopify object
          printJob.data.shipping_address = order.shipping_address; // shopify object
          // printJob.data.pdfNamePrefix = order.id;

          // create dowwnload
          const download = await DB.Download.create({
            id: printJob.id,
            resellerId: store.resellerId,
            productId: design.productId,
            designId: design.id,
            linkedOrderId: '',
            variationName: printJob.productVariantionName || '',
            pdf: '',
          });
          printJob.data.pdfNamePrefix = `${VarHelper.fourDigitsNumber(download.queueId)}-${variant.name.replace(/\s/g, '_').toUpperCase()}-${printJob.quantity}-B`;
          printJob.readyForPrint = true;
          await printJob.save();

          // generate pdf in background
          (async () => {
            await generatePdf({
              id: printJob.id,
              designId: printJob.designId,
              images: printJob.artworkUrls,
              data: printJob.data,
              forceVarnish: false,
            });
          })();
        })();
        continue;
      }

      // findCustomArtwork
      (async () => {
        if (!findDesignID) return;
        const customArtworkUrl = `https://print-manager-media.s3.eu-west-1.amazonaws.com/msc-custom-artwork/${findCustomArtwork.value}`;

        const design = await DB.Design.findByPk(findDesignID.value);
        if (!design) {
          console.log('DESIGN NOT FOUND');
          return;
        }
        const product = await DB.Product.findByPk(design.productId);
        if (!product) {
          console.log('PRODUCT NOT FOUND');
          return;
        }
        const artworkUrl = await generateCustomArtwork({
          width: product.physicalWidth,
          height: product.physicalHeight,
          imageUrl: customArtworkUrl,
        });
        const printJob = await DB.PrintJob.create({
          id: VarHelper.genId(),
          designId: findDesignID.value,
          quantity: variant.quantity,
          clientId: store.resellerId,
          productId: product.id,
          productVariantionName: '',
          previewUrl: customArtworkUrl,
          artworkUrls: [artworkUrl],
          productName: `${design.name} - Custom Artwork - ${customer.last_name}`,
          data: {
            orderId: order.id,
            customer: order.customer,
            shipping_address: order.shipping_address,
            product: {
              physicalHeight: product.physicalHeight,
              physicalWidth: product.physicalWidth,
              printAreas: product.printAreas,
            }
          },
          isPaid: false,
          isPrinted: false,
          isPDFDownloaded: false,
          isRePrinted: false,
        });

        // create dowwnload
        const download = await DB.Download.create({
          id: printJob.id,
          resellerId: store.resellerId,
          productId: design.productId,
          designId: design.id,
          linkedOrderId: '',
          variationName: printJob.productVariantionName || '',
          pdf: '',
        });
        printJob.data.pdfNamePrefix = `${VarHelper.fourDigitsNumber(download.queueId)}-${variant.name.replace(/\s/g, '_').toUpperCase()}-${printJob.quantity}-B`;
        printJob.readyForPrint = true;
        await printJob.save();

        // generate pdf in background
        (async () => {
          await generatePdf({
            id: printJob.id,
            designId: printJob.designId,
            images: printJob.artworkUrls,
            data: printJob.data,
            forceVarnish: false,
          });
        })();
      })();
    }

    return { success: true, data: request.body };
  }
};

export default new ShopifyWebhook();
