{"id": 4746297573458, "admin_graphql_api_id": "gid://shopify/Order/4746297573458", "app_id": 580111, "browser_ip": "************", "buyer_accepts_marketing": false, "cancel_reason": null, "cancelled_at": null, "cart_token": "4d6b2e079306a76fae1f68106b47ba77", "checkout_id": 26068482719826, "checkout_token": "daa7d2427ce560a4a74259b6c1b71e63", "client_details": {"accept_language": "en-GB,en;q=0.9,en-US;q=0.8,en-NZ;q=0.7", "browser_height": 584, "browser_ip": "************", "browser_width": 1103, "session_hash": null, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/106.0.1370.52"}, "closed_at": null, "confirmed": true, "contact_email": "<EMAIL>", "created_at": "2022-10-24T16:37:20+01:00", "currency": "GBP", "current_subtotal_price": "4.25", "current_subtotal_price_set": {"shop_money": {"amount": "4.25", "currency_code": "GBP"}, "presentment_money": {"amount": "4.25", "currency_code": "GBP"}}, "current_total_discounts": "0.00", "current_total_discounts_set": {"shop_money": {"amount": "0.00", "currency_code": "GBP"}, "presentment_money": {"amount": "0.00", "currency_code": "GBP"}}, "current_total_duties_set": null, "current_total_price": "8.20", "current_total_price_set": {"shop_money": {"amount": "8.20", "currency_code": "GBP"}, "presentment_money": {"amount": "8.20", "currency_code": "GBP"}}, "current_total_tax": "1.37", "current_total_tax_set": {"shop_money": {"amount": "1.37", "currency_code": "GBP"}, "presentment_money": {"amount": "1.37", "currency_code": "GBP"}}, "customer_locale": "en", "device_id": null, "discount_codes": [], "email": "<EMAIL>", "estimated_taxes": false, "financial_status": "paid", "fulfillment_status": null, "gateway": "shopify_payments", "landing_site": "/collections/christmas-cards", "landing_site_ref": null, "location_id": null, "merchant_of_record_app_id": null, "name": "#84331", "note": null, "note_attributes": [{"name": "stackable_session_id", "value": "4a7f8334-6584-4d67-9e22-b4128504e5a1"}], "number": 83331, "order_number": 84331, "order_status_url": "https://shop.macmillan.org.uk/25175523410/orders/7be9499a47142f21b71d318ed5e6b8a4/authenticate?key=f9cd986eebe96cad1af7d5bea068c952", "original_total_duties_set": null, "payment_gateway_names": ["paypal", "shopify_payments"], "phone": null, "presentment_currency": "GBP", "processed_at": "2022-10-24T16:37:19+01:00", "processing_method": "direct", "reference": null, "referring_site": "https://r.search.yahoo.com/_ylt=AwrIQZFfrlZjpGAAwYF3Bwx.;_ylu=Y29sbwMEcG9zAzIEdnRpZAMEc2VjA3Ny/RV=2/RE=1666653920/RO=10/RU=https://shop.macmillan.org.uk/collections/christmas-cards/RK=2/RS=WHkR5tGivxCKYAmWFKoeH.WfW2U-", "source_identifier": null, "source_name": "web", "source_url": null, "subtotal_price": "4.25", "subtotal_price_set": {"shop_money": {"amount": "4.25", "currency_code": "GBP"}, "presentment_money": {"amount": "4.25", "currency_code": "GBP"}}, "tags": "", "tax_lines": [{"price": "1.37", "rate": 0.2, "title": "GB VAT", "price_set": {"shop_money": {"amount": "1.37", "currency_code": "GBP"}, "presentment_money": {"amount": "1.37", "currency_code": "GBP"}}, "channel_liable": false}], "taxes_included": true, "test": false, "token": "7be9499a47142f21b71d318ed5e6b8a4", "total_discounts": "0.00", "total_discounts_set": {"shop_money": {"amount": "0.00", "currency_code": "GBP"}, "presentment_money": {"amount": "0.00", "currency_code": "GBP"}}, "total_line_items_price": "4.25", "total_line_items_price_set": {"shop_money": {"amount": "4.25", "currency_code": "GBP"}, "presentment_money": {"amount": "4.25", "currency_code": "GBP"}}, "total_outstanding": "0.00", "total_price": "8.20", "total_price_set": {"shop_money": {"amount": "8.20", "currency_code": "GBP"}, "presentment_money": {"amount": "8.20", "currency_code": "GBP"}}, "total_shipping_price_set": {"shop_money": {"amount": "3.95", "currency_code": "GBP"}, "presentment_money": {"amount": "3.95", "currency_code": "GBP"}}, "total_tax": "1.37", "total_tax_set": {"shop_money": {"amount": "1.37", "currency_code": "GBP"}, "presentment_money": {"amount": "1.37", "currency_code": "GBP"}}, "total_tip_received": "0.00", "total_weight": 189, "updated_at": "2022-10-24T16:37:23+01:00", "user_id": null, "billing_address": {"first_name": "FRANK", "address1": "Apartment 9, Wilton Court Southbank Road", "phone": null, "city": "<PERSON><PERSON><PERSON>", "zip": "CV8 1RX", "province": "England", "country": "United Kingdom", "last_name": "HADFIELD", "address2": "", "company": "", "latitude": 52.3443397, "longitude": -1.5791144, "name": "FRANK HADFIELD", "country_code": "GB", "province_code": "ENG"}, "customer": {"id": *************, "email": "<EMAIL>", "accepts_marketing": false, "created_at": "2022-10-24T16:30:50+01:00", "updated_at": "2022-10-24T16:37:21+01:00", "first_name": "FRANK", "last_name": "HADFIELD", "state": "disabled", "note": null, "verified_email": true, "multipass_identifier": null, "tax_exempt": false, "tags": "", "currency": "GBP", "phone": null, "accepts_marketing_updated_at": "2022-10-24T16:30:50+01:00", "marketing_opt_in_level": null, "email_marketing_consent": {"state": "not_subscribed", "opt_in_level": "single_opt_in", "consent_updated_at": null}, "sms_marketing_consent": null, "admin_graphql_api_id": "gid://shopify/Customer/*************", "default_address": {"id": *************, "customer_id": *************, "first_name": "FRANK", "last_name": "HADFIELD", "company": "", "address1": "Apartment 9, Wilton Court Southbank Road", "address2": "", "city": "<PERSON><PERSON><PERSON>", "province": "England", "country": "United Kingdom", "zip": "CV8 1RX", "phone": null, "name": "FRANK HADFIELD", "province_code": "ENG", "country_code": "GB", "country_name": "United Kingdom", "default": true}}, "discount_applications": [], "fulfillments": [], "line_items": [{"id": **************, "admin_graphql_api_id": "gid://shopify/LineItem/**************", "fulfillable_quantity": 1, "fulfillment_service": "manual", "fulfillment_status": null, "gift_card": false, "grams": 189, "name": "Moonlit Tree and Fox Christmas Card - 10 Pack", "price": "4.25", "price_set": {"shop_money": {"amount": "4.25", "currency_code": "GBP"}, "presentment_money": {"amount": "4.25", "currency_code": "GBP"}}, "product_exists": true, "product_id": *************, "properties": [], "quantity": 1, "requires_shipping": true, "sku": "MCS2506", "taxable": true, "title": "Moonlit Tree and Fox Christmas Card - 10 Pack", "total_discount": "0.00", "total_discount_set": {"shop_money": {"amount": "0.00", "currency_code": "GBP"}, "presentment_money": {"amount": "0.00", "currency_code": "GBP"}}, "variant_id": 32747341578322, "variant_inventory_management": "shopify", "variant_title": "", "vendor": "Macmillan Cancer Support Shop", "tax_lines": [{"channel_liable": false, "price": "0.71", "price_set": {"shop_money": {"amount": "0.71", "currency_code": "GBP"}, "presentment_money": {"amount": "0.71", "currency_code": "GBP"}}, "rate": 0.2, "title": "GB VAT"}], "duties": [], "discount_allocations": []}], "payment_details": {"credit_card_bin": "492910", "avs_result_code": "Y", "cvv_result_code": "P", "credit_card_number": "•••• •••• •••• 9014", "credit_card_company": "Visa", "credit_card_name": "B J HADFIELD", "credit_card_wallet": null, "credit_card_expiration_month": 12, "credit_card_expiration_year": 2026}, "payment_terms": null, "refunds": [], "shipping_address": {"first_name": "FRANK", "address1": "Apartment 9, Wilton Court Southbank Road", "phone": null, "city": "<PERSON><PERSON><PERSON>", "zip": "CV8 1RX", "province": "England", "country": "United Kingdom", "last_name": "HADFIELD", "address2": "", "company": "", "latitude": 52.3443397, "longitude": -1.5791144, "name": "FRANK HADFIELD", "country_code": "GB", "province_code": "ENG"}, "shipping_lines": [{"id": *************, "carrier_identifier": "1db2e5576ce75de8b6f398fd04fe123e", "code": "flat-rate", "delivery_category": null, "discounted_price": "3.95", "discounted_price_set": {"shop_money": {"amount": "3.95", "currency_code": "GBP"}, "presentment_money": {"amount": "3.95", "currency_code": "GBP"}}, "phone": null, "price": "3.95", "price_set": {"shop_money": {"amount": "3.95", "currency_code": "GBP"}, "presentment_money": {"amount": "3.95", "currency_code": "GBP"}}, "requested_fulfillment_service_id": null, "source": "Advanced Shipping Rules", "title": "2nd Class (Standard Delivery)", "tax_lines": [{"channel_liable": false, "price": "0.66", "price_set": {"shop_money": {"amount": "0.66", "currency_code": "GBP"}, "presentment_money": {"amount": "0.66", "currency_code": "GBP"}}, "rate": 0.2, "title": "GB VAT"}], "discount_allocations": []}]}