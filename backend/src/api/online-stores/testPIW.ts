import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { checkA<PERSON>en, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON>elper } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import axios from 'axios';
import { MATCH_SHOPIFY_PRODUCTS, genArtworkUrl, genPreviewUrl } from './partner-in-wine/index';

class TestPIW implements TypeAPIHandler {

  url = "/api/online-stores/partner-in-wine/test-output";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      shopifyProductId: Joi.number(),
      orientation: Joi.string(),
      font: Joi.string(),
      color: Joi.string(),
      productColor: Joi.string(),
      text: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { shopifyProductId, orientation, font, color, productColor, text } = request.body;
    const productId = MATCH_SHOPIFY_PRODUCTS()[shopifyProductId].productLibraryId;
    const product = await DB.Product.findByPk(productId);
    const artwork = await genArtworkUrl(shopifyProductId, { text, orientation, font, color }, product);
    const preview = await genPreviewUrl(shopifyProductId, { orientation, font, color, productColor, text });

    return {
      success: true,
      data: {
        artwork,
        preview,
      },
    }
  };
}

export default new TestPIW();