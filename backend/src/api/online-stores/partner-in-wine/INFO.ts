const TIMESTAMP_NEW_FONT = 1691558423766;


export const MATCH_SHOPIFY_PRODUCTS = () => {
  // const isNewFont = new Date().getTime() > TIMESTAMP_NEW_FONT;
  const isNewFont = false;
  return {
    6816438288481: {
      productLibraryId: '318380546570',
      name: '<PERSON><PERSON>',
      previewImage: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Wine_Pink_Customised_Preview_No_Logo.png',
      productColors: {
        40118786359393: { color: 'Black', preview: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Wine_Black_Customised_Preview_No_Logo.png' },
        40118786392161: { color: 'White', preview: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Wine_White_Customised_Preview_No_Logo.png' },
        40118786424929: { color: 'Pink', preview: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Wine_Pink_Customised_Preview_No_Logo.png' },
        40118786457697: { color: 'Desert Sand', preview: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Wine_Desert_Sand_Customised_Preview_No_Logo.png' },
        40118786490465: { color: 'Turquoise', preview: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Wine_Turqoise_Customised_Preview_No_Logo.png' },
        40118786523233: { color: 'Lavender', preview: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Wine_Lavender_Customised_Preview_No_Logo.png' },
        40118786556001: { color: 'Khaki', preview: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Wine_Khaki_Customised_Preview_No_Logo.png' },
        40118786588769: { color: 'Merlot', preview: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Wine_Merlot_Customised_Preview_No_Logo.png' },
      },
      preview: {
        lettering: {
          fontSize: isNewFont ? 80 : 100
        },
        initial: {
          fontSize: 40
        }
      },
      print: {
        lettering: {
          fontSize: isNewFont ? 640 * 0.6 : 640 * 0.8,
          alignItems: 'center',
          paddingBottom: '0%',
          transform: 'rotate(270deg)',
        },
        initial: {
          alignItems: 'flex-end',
          paddingBottom: '4.5%',
          fontSize: 280 * 1.28,
        }
      },
      fontSize: {
        horizontal: 280 * 1.28,
        vertical: isNewFont ? 480 * 0.3 : 480 * 0.5
      },
      sizeForMultipleRowsRatio: [0.9, 0.6],
    },
    6834889883745: {
      productLibraryId: '782071196240',
      name: 'Tumbler',
      previewImage: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Tumblr_Customised_Preview_No_Logo.png',
      productColors: {
        40231026163809: { color: 'Black', preview: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Tumblr_Customised_Preview_No_Logo.png' },
        40231026196577: { color: 'White', preview: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Tumblr_White_Sand_Customised_Preview_No_Logo.png' },
        40231026229345: { color: 'Pink', preview: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Tumblr_Pink_Sand_Customised_Preview_No_Logo.png' },
        40231026262113: { color: 'Desert Sand', preview: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Tumblr_Desert_Sand_Customised_Preview_No_Logo.png' },
        40231026294881: { color: 'Turquoise', preview: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Tumblr_Turquoise_Sand_Customised_Preview_No_Logo.png' },
        40231026327649: { color: 'Lavender', preview: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Tumblr_Lavender_Sand_Customised_Preview_No_Logo.png' },
        40231026360417: { color: 'Khaki', preview: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Tumblr_Khaki_Sand_Customised_Preview_No_Logo.png' },
        40231026393185: { color: 'Merlot', preview: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Tumblr_Merlot_Sand_Customised_Preview_No_Logo.png' },
      },
      fontSize: {
        horizontal: 140,
        vertical: isNewFont ? 360 * 1.3584 * 0.8 : 360 * 1.3584
      },
      preview: {
        lettering: {
          fontSize: isNewFont ? 120 : 140
        },
        initial: {
          fontSize: 80
        }
      },
      print: {
        lettering: {
          fontSize: isNewFont ? 360 * 1.3584 * 0.8 : 360 * 1.3584 * 0.77,
          alignItems: 'flex-end',
          paddingBottom: '2%',
          transform: 'none',
        },
        initial: {
          alignItems: 'flex-end',
          paddingBottom: '1%',
          fontSize: 234 * 1.3584,
        }
      },
      sizeForMultipleRowsRatio: [1, 1],
    },
    6838215999585: {
      productLibraryId: '528946868447',
      name: 'Ice Queen',
      previewImage: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Ice_Queen_Pink_no_logo.png',
      productColors: {
        40245010235489: { color: 'Black', preview: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Ice_Queen_Black_no_logo.png' },
        40245010268257: { color: 'White', preview: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Ice_Queen_White_no_logo.png' },
        40245010301025: { color: 'Pink', preview: 'https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/Ice_Queen_Pink_no_logo.png' }
      },
      fontSize: {
        horizontal: 280 * 1.28,
        vertical: isNewFont ? 480 * 0.3 : 480 * 0.5
      },
      horizontalBottom: 0.117,
      preview: {
        lettering: {
          fontSize: isNewFont ? 120 : 140
        },
        initial: {
          fontSize: 40
        }
      },
      print: {
        lettering: {
          fontSize: isNewFont ? 640 * 0.4 : 640 * 0.6,
          alignItems: 'center',
          paddingBottom: '0%',
          transform: 'rotate(270deg)',
        },
        initial: {
          alignItems: 'flex-end',
          paddingBottom: '4.5%',
          fontSize: 280 * 1.28,
        }
      },
      sizeForMultipleRowsRatio: [0.9, 0.6],
    }
  };
}
