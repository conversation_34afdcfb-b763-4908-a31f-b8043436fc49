
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "helpers";
const puppeteer = require('puppeteer');
import { AlexanderLettering, BigCaslon, PinkChampagne } from './partnerInWineFont';
const fs = require('fs');
import { MATCH_SHOPIFY_PRODUCTS } from './INFO';
import { TProduct } from 'type';
import { ERROR, MM_TO_PIXEL } from 'const';
var htmlEncode = require('js-htmlencode');

export const genArtworkUrl = async (shopifyProductId, { text, orientation, font, color }, product : TProduct) => {
  const containerWidth = Math.round(product.physicalWidth * MM_TO_PIXEL);
  const containerHeight = Math.round(product.physicalHeight * MM_TO_PIXEL);
  
  const fileName = 'tempSVGFile-' + new Date().getTime();
  const tempPNGFile = fileName + '.png';

  const data = MATCH_SHOPIFY_PRODUCTS()[shopifyProductId];
  const isLettering = font === 'Alexander Lettering' || font === 'Pink Champagne';
  const style = isLettering ? data.print?.lettering : data.print?.initial;
  console.log('style', style);
  console.log('encode text', htmlEncode(text));

  const hasLineBreak = text.includes('/');

  const adjustFontSize = (fontSize) => {
    if (text.length > 5) return fontSize;
    return fontSize * 1.35;

    // let numberOfCapitalize = 0;
    // const reg = new RegExp("[A-Z]");
    // for (let i=0; i<text.length; i++) {
    //   const character = text[i];
    //   const isValid = reg.test(character)
    //   if (isValid) {
    //     numberOfCapitalize += 1;
    //   }
    // }
    // if (numberOfCapitalize / text.length <= 0.7) return fontSize;
    // return fontSize * 0.7;
  }

  const convertToPNG = async () => {
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox'],
    });
    const page = await browser.newPage();
    await page.setDefaultNavigationTimeout(0);
    const adjustedFontSize = adjustFontSize(style?.fontSize);
    const sizeForMultipleRowsRatio = data.sizeForMultipleRowsRatio;
    const html = `
      <html>
        <head>
          <style>
            /*
            @font-face {
              font-family: 'Alexander Lettering';
              src: url('https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/AlexanderLettering.woff2') format('woff2'),
                  url('https://print-manager-media.s3.eu-west-1.amazonaws.com/PartnerInWine/AlexanderLettering.woff') format('woff');
              font-weight: normal;
              font-style: normal;
              font-display: swap;
            }
            */
            ${
              font === 'Alexander Lettering' ? AlexanderLettering :
              (font === 'Pink Champagne' ? PinkChampagne : BigCaslon)
            }
            html, body {
              background-color: transparent;
              margin: 0;
              padding: 0;
            }
            #preview-customized {
              width: ${containerWidth}px;
              height: ${containerHeight}px;
              position: relative;
            }
            #preview-customized-inner {
              position: absolute;
              top: 0;
              left: 0;
              bottom: 0;
              right: 0;
            }
            .customized-content-area {
              position: absolute;
              top: 0;
              left: 0;
              bottom: 0;
              right: 0;
              display: flex;
              justify-content: center;
              align-items: ${style.alignItems};
            }
            .customized-content-area pre {
              display: block;
              letter-spacing: 0.2px;
              white-space: nowrap;
              padding-bottom: ${style?.paddingBottom || '0'};
              color: ${color};
              font-family: ${font};
              font-size: ${adjustedFontSize}px;
              ${!hasLineBreak ? `font-size: ${adjustedFontSize}px;` : `font-size: ${adjustedFontSize * sizeForMultipleRowsRatio[0]}px;`}
              ${!hasLineBreak ? '' : `line-height: ${adjustedFontSize * sizeForMultipleRowsRatio[1]}px;`}
              transform: ${style?.transform || 'none'};
              margin: 0px;
              ${color === 'white' ? '-webkit-text-stroke: 5px white;' : ''}
              text-align: center;
            }
          </style>
        </head>
        <body>
          <div id="preview-customized">
            <div id="preview-customized-inner">
              <div class="customized-content-area">
                <pre>${text.replace(/\//, '<br />')}</pre>
              </div>
            </div>
          </div>
        </body>
      </html>
    `;
    await page.setContent(html);
    await page.setViewport({ width: containerWidth, height: containerHeight });
    // await page.waitForNavigation({waitUntil: 'networkidle2'})
    await page.screenshot({ path: tempPNGFile, omitBackground: true });
    await browser.close();
    fs.writeFileSync('html-art-'+shopifyProductId + '.html', html);
  }

  const toBeSavedFileNameInS3 = `artwork-for-product-${shopifyProductId}-${new Date().getTime()}.png`;
  await convertToPNG();
  const s3Url = await AWSHelper.upload({
    key: `print-prepare/${toBeSavedFileNameInS3}`,
    filePath: tempPNGFile,
  });
  return s3Url;
};