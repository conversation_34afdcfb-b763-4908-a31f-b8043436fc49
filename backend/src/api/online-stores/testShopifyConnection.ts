import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON>ar<PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import axios from 'axios';
import { checkReseller } from "api/api-middlewares/authen";

class TestShopifyConnection implements TypeAPIHandler {

  url = "/api/online-stores/test-shopify-connection";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      url: Joi.string(),
      token: Joi.string(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkReseller
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;
    const { url, token } = body;
    // if (user.role !== 'reseller') throw new Error(ERROR.PERMISSION_DENIED);

    const axiosRes : any = await axios.request({
      url: `${url}/admin/api/2023-04/products.json`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': token,
      },
      data: JSON.stringify({
        product: {
          title: 'Hello World from MSupply Print Manager',
          product_type: 'MSupply',
          published: false,
          status: 'draft',
        }
      })
    });
    const product = axiosRes.data.product;

    return {
      success: true,
      data: product,
    }
  };
}

export default new TestShopifyConnection();
