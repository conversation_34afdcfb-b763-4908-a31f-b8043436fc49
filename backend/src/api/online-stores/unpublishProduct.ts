import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON>en, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FileHelper } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import axios from 'axios';
import { checkReseller } from "api/api-middlewares/authen";
import Etsy from "helpers/EtsyHelper";

const path = require('path');
const fs = require('fs');

class UnPublishProduct implements TypeAPIHandler {
  url = "/api/online-stores/unpublish-product";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      productId: Joi.number().required(),
      storeId: Joi.string().required(),
      designId: Joi.string().required(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;
    const { productId, storeId, designId } = body;
    const design = await DB.Design.findByPk(designId);
    if (!design) {
      throw new Error('Design not found');
    }
    const store = await DB.OnlineStore.findByPk(storeId);
    if (!store) {
      throw new Error('Store not found. Please connect your store');
    }

    if (store.type === 'etsy') {
      if (store.data?.etsyAccessToken && store.data?.etsyRefreshToken) {
        const etsy = new Etsy(store.data?.etsyAccessToken, store.data?.etsyRefreshToken);
        await etsy.deleteListing(productId);
      }
    } else {
      const token = store.data?.shopifyAccessToken;
      await axios.request({
        url: `${store.url}/admin/api/2023-07/products/${productId}.json`,
        method: 'delete',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Access-Token': token,
        },
      });
    }

    const storeProducts = (design.products || []).filter(v => v.productId !== productId && v.storeId !== storeId);
    console.log('storeProducts', storeProducts);
    design.products = storeProducts;
    await design.save();

    return {
      success: true,
    }

  };
}

export default new UnPublishProduct();
