import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { Var<PERSON><PERSON><PERSON> } from "helpers";
import { ERROR } from 'const';
import Joi = require("joi");
import axios from 'axios';
import { checkReseller } from "api/api-middlewares/authen";
import RolesHelper from "helpers/RolesHelper";

class UpsertOnlineStore implements TypeAPIHandler {

  url = "/api/online-stores";
  method = "POST";
  apiSchema = {
    body: Joi.object({
      id: Joi.string(),
      name: Joi.string(),
      type: Joi.string(),
      url: Joi.string(),
      data: Joi.any(),
    }),
  }

  preHandler = combineMiddlewares([
    validateRequest(this.apiSchema),
    checkAuthen,
    checkReseller
  ]);

  handler = async (request: TRequestUser, reply) => {
    const { body, user } = request;
    // if (user.role !== 'reseller') throw new Error(ERROR.PERMISSION_DENIED);
    // UPDATE EXISTING
    if (body.id) {
      const find = await DB.OnlineStore.findByPk(request.body.id);
      if (!!find) {
        if (find.resellerId !== RolesHelper.getResellerId(user)) throw new Error(ERROR.PERMISSION_DENIED);
        for (let key in body) {
          find[key] = body[key];
        }
        await find.save();
        return { success: true, data: find };
      }
    }
    // CREATE NEW

    const storeId = VarHelper.genId();
    // firstly we have to create webhook

    const url = body.url;
    const token = body.data?.shopifyAccessToken;
    if (!!url && !!token) {
      try {
        const axiosRes: any = await axios.request({
          url: `${url}/admin/api/2023-04/webhooks.json`,
          method: 'post',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': token,
          },
          data: JSON.stringify({
            webhook: {
              topic: 'orders/create',
              address: `${process.env.BACKEND_CMS_URL || 'https://msc.personify.tech'}/api/online-stores/${storeId}/shopify-webhook`,
              format: 'json',
              fields: [],
            }
          })
        });
        if (!axiosRes.data.webhook?.id) {
          throw new Error('Can not create Webhook, please check your token permission');
        }
      } catch (err) {
        throw new Error('Can not create Webhook, please check your token permission');
      }
    }

    // 
    const data = await DB.OnlineStore.create({
      id: storeId,
      ...body,
      resellerId: user.resellerId || user.id,
    });

    return {
      success: true,
      data,
    }
  };
}

export default new UpsertOnlineStore();
