import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TRequestUser } from "type";
import { check<PERSON><PERSON><PERSON>, checkAuthenOptional, combineMiddlewares, validateRequest } from "api/api-middlewares";
import { DB } from "db";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>elper } from "helpers";
import { ERROR, MM_TO_PIXEL } from 'const';
import Joi = require("joi");
import axios from 'axios';
import { generatePdf } from '../pdf/generatePdf';
import { TProduct } from 'type';
const sampleProduct = require('./sample-product.json');
import { AlexanderLettering, BigCaslon } from './partnerInWineFont';
import { Op } from 'sequelize';
const fs = require('fs');
import { MATCH_SHOPIFY_PRODUCTS, genArtworkUrl, genPreviewUrl } from './partner-in-wine/index';

interface IShopifyOrder {
  id: number,
  customer: {
    email: string,
    first_name: string,
    last_name: string,
    [other: string]: any,
  },
  line_items: Array<{
    id: string | number, // variant id
    product_id: number,
    properties: Array<{
      name: string,
      value: string,
    }>,
    quantity: number,
    name: string,
  }>,
  [other: string]: any,
}

class ShopifyWebhook implements TypeAPIHandler {
  url = "/api/online-stores/partner-in-wine/shopify-webhook";
  method = "POST";

  preHandler = combineMiddlewares([
    // validateRequest(this.apiSchema),
  ]);

  handler = async (request: TRequestUser, reply) => {
    console.log('PARTNER IN WINE - RECEIVE SHOPIFY WEBHOOK', new Date().toISOString());
    // console.log(request.body);

    if (!request.body.dev)
      DB.GeneralData.create({
        id: VarHelper.genId(),
        type: 'logs',
        name: 'shopify-webhook-partner-in-wine',
        field1: request.params.id,
        field2: '',
        userId: '1',
        data: request.body,
        publicPermission: {
          c: false, r: false, u: false, d: false,
        }
      })

    const body: IShopifyOrder = request.body;
    const order = body;
    if (!body.id) throw new Error('Order id missing');

    const { customer, line_items } = body;

    line_items.forEach(async variant => {
      if (!MATCH_SHOPIFY_PRODUCTS()[variant.product_id]) return;
      const productLibraryId = MATCH_SHOPIFY_PRODUCTS()[variant.product_id].productLibraryId;
      let product = await DB.Product.findByPk(productLibraryId);
      // if (!product) {
      //   product = sampleProduct.data;
      // }

      const properties = {
        text: fromProperty(variant.properties, 'Personalise Text', ''),
        orientation: fromProperty(variant.properties, 'Personalise Orientation', 'vertical'),
        font: fromProperty(variant.properties, 'Personalise Font', 'Alexander Lettering'),
        color: fromProperty(variant.properties, 'Personalise Color', 'black'),
        productColor: fromProperty(variant.properties, 'Personalise Product Color', 'Black')
      }

      const previewUrl = await genPreviewUrl(variant.product_id, properties);
      const artworkUrl = await genArtworkUrl(variant.product_id, properties, product);

      console.log('previewUrl', previewUrl);
      console.log('artworkUrl', artworkUrl);

      if (request.body.dev) return;


      const checkExisting = await DB.PrintJob.findOne({
        where: {
          [Op.and]: [
            {
              data: {
                [Op.like]: `%${body.id}%`,
              }
            },
            {
              data: {
                [Op.like]: `%${variant.id}%`,
              },
            }
          ]
        }
      });
      if (checkExisting) {
        console.log('ALREADY EXISTS', checkExisting.id);
        return;
      }

      const printJob = await DB.PrintJob.create({
        id: VarHelper.genId(),
        quantity: variant.quantity,
        clientId: 'partnet-in-wine',
        productName: `${variant.name} - ${customer.last_name}`,
        productId: product.id,
        designId: '',
        productVariantionName: 'Var + No Mirr',
        previewUrl,
        artworkUrls: [artworkUrl],
        data: {
          orderId: order.id,
          variantId: variant.id,
          product: {
            physicalWidth: product.physicalWidth,
            physicalHeight: product.physicalHeight,
            printAreas: product.printAreas,
          },
          customer: order.customer, // shopify object
          shipping_address: order.shipping_address, // shopify object
        },
        isPaid: false,
        isPrinted: false,
        isRePrinted: false,
        isPDFDownloaded: false,
      })
      // create dowwnload
      const download = await DB.Download.create({
        id: printJob.id,
        resellerId: 'partnet-in-wine',
        productId: product.id,
        designId: '',
        linkedOrderId: '',
        variationName: printJob.productVariantionName || '',
        pdf: '',
      });
      printJob.data.pdfNamePrefix = `${VarHelper.fourDigitsNumber(download.queueId)}-${variant.name.replace(/\s/g, '_').toUpperCase()}-${printJob.quantity}-B`;
      printJob.readyForPrint = true;
      await printJob.save();

      console.log('PRINT JOB', printJob.id);
      console.log(JSON.stringify(printJob));

      // generate pdf in background
      (async () => {
        await generatePdf({
          id: printJob.id,
          designId: printJob.designId,
          images: printJob.artworkUrls,
          data: printJob.data,
          forceVarnish: true,
          piw: true,
        });
      })();
    });

    return { success: true };
  }
};

const fromProperty = (properties, label, defaultValue) => {
  const findItem = (properties || []).find(val => val.name === label);
  return !findItem ? defaultValue : findItem.value;
}

export default new ShopifyWebhook();