import { getAllProductVariants } from "./shopify-helper";
import { S3Client, GetObjectCommand } from "@aws-sdk/client-s3";
import * as fs from 'fs';

var CronJob = require('cron').CronJob;
const AWS_ACCESS_KEY_ID = "********************";
const AWS_SECRET_ACCESS_KEY = "Ihtv6IeDvAN2bvQcp7p2WfG4/JYWDW053JVUsRUP";
const AWS_REGION = "eu-west-2";
const AWS_BUCKET = "msc-shopify-budget-approval";

async function downloadCsvFromS3(s3Key: string) {
  const s3Client = new S3Client({
    region: AWS_REGION,
    credentials: {
      accessKeyId: AWS_ACCESS_KEY_ID,
      secretAccessKey: AWS_SECRET_ACCESS_KEY
    }
  });

  const command = new GetObjectCommand({
    Bucket: AWS_BUCKET,
    Key: s3Key
  });

  const response = await s3Client.send(command);
  const csvContent = await response.Body?.transformToString();
  return csvContent;
}

export const execute = async () => {
  console.log('📦 Fetching all product variants from Shopify...');
  const skuMapping = await getAllProductVariants();
  // const skuMapping = fs.readFileSync('sku-variant-mapping.json', 'utf8');
  console.log('📦 Saving SKU mapping to file...', Object.keys(skuMapping).length);
  fs.writeFileSync('bc-import-data/sku-variant-mapping.json', JSON.stringify(skuMapping, null, 2));

  // Load company location mapping
  const csvContent = await downloadCsvFromS3("company_locations.csv");
  if (csvContent) {
    console.log('📦 Saving company locations CSV to file...');
    fs.writeFileSync('bc-import-data/company_locations.csv', csvContent);
  }
};

export const syncCustomerMappingBC = () => {
  var job = new CronJob('0 */3 * * *', function() {
    console.log('Running customer mapping sync...');
    execute().catch(err => {
      console.error('Error in sync BC data:', err);
    });
  }, null, false, 'Europe/London');
  job.start();
};
