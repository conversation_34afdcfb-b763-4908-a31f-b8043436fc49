import { DB } from 'db';
import { Var<PERSON><PERSON><PERSON> } from 'helpers';
import ShopifyHelper from 'helpers/ShopifyHelperNew';

export const syncShopifyProducts = async () => {
  const shopifyProducts = await ShopifyHelper.getAllProducts();
  // await ShopifyHelper.registerWebhook({
  //   topic: 'PRODUCTS_CREATE',
  //   callbackUrl: `${process.env.APP_URL}/api/sync-products/webhook`,
  // });
  // await ShopifyHelper.registerWebhook({
  //   topic: 'PRODUCTS_UPDATE',
  //   callbackUrl: `${process.env.APP_URL}/api/sync-products/webhook`,
  // });
  // await ShopifyHelper.registerWebhook({
  //   topic: 'PRODUCTS_DELETE',
  //   callbackUrl: `${process.env.APP_URL}/api/sync-products/webhook`,
  // });
  // console.log("added webhooks");
  console.log("fetch shopify products: ", shopifyProducts.length);
  for (const product of shopifyProducts) {
    await DB.SyncedProduct.upsert(VarHelper.parseShopifyProduct(product));
  }
};
