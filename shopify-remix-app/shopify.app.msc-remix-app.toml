# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "d61f6267ae7664fddda1d55d3e346f3d"
name = "msc-remix-app"
handle = "msc-remix-app-1"
application_url = "https://rochester-oriented-fe-many.trycloudflare.com"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "msc-teleorder-test.myshopify.com"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_customers,read_orders,write_products"

[auth]
redirect_urls = [
  "https://rochester-oriented-fe-many.trycloudflare.com/auth/callback",
  "https://rochester-oriented-fe-many.trycloudflare.com/auth/shopify/callback",
  "https://rochester-oriented-fe-many.trycloudflare.com/api/auth/callback"
]

[webhooks]
api_version = "2025-07"

[app_proxy]
url = "https://rochester-oriented-fe-many.trycloudflare.com/dashboard"
subpath = "dashboard"
prefix = "apps"

[pos]
embedded = false
