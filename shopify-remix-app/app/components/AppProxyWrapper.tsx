import { useEffect, useState } from "react";

interface AppProxyWrapperProps {
  children: React.ReactNode;
}

export default function AppProxyWrapper({ children }: AppProxyWrapperProps) {
  const [isAppProxy, setIsAppProxy] = useState(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    
    // Detect if running in Shopify App Proxy context
    const isInAppProxy = typeof window !== 'undefined' && (
      window.location.pathname.includes('/apps/dashboard') ||
      window.location.hostname.includes('.myshopify.com') ||
      (window as any).Shopify
    );
    
    setIsAppProxy(isInAppProxy);
    
    console.log("App Proxy Detection:", {
      isInAppProxy,
      pathname: window.location.pathname,
      hostname: window.location.hostname,
      hasShopify: !!(window as any).Shopify
    });

    // If in app proxy, add special handling for events
    if (isInAppProxy) {
      // Override event handling for app proxy context
      const originalAddEventListener = Element.prototype.addEventListener;
      Element.prototype.addEventListener = function(type, listener, options) {
        console.log("App Proxy: Event listener added for", type, "on", this);
        return originalAddEventListener.call(this, type, listener, options);
      };

      // Add global event delegation for app proxy
      document.addEventListener('click', function(e) {
        console.log("App Proxy: Global click detected on", e.target);
        
        // Handle button clicks specifically
        if (e.target instanceof HTMLButtonElement) {
          console.log("App Proxy: Button clicked", e.target.textContent);
          
          // Trigger custom events for buttons
          const customEvent = new CustomEvent('appProxyButtonClick', {
            detail: {
              button: e.target,
              text: e.target.textContent
            }
          });
          document.dispatchEvent(customEvent);
        }
      }, true); // Use capture phase

      // Listen for custom events
      document.addEventListener('appProxyButtonClick', function(e: any) {
        console.log("App Proxy: Custom button event", e.detail);
        
        // Handle specific buttons
        if (e.detail.text?.includes('Download Report')) {
          alert('Download Report clicked in App Proxy!');
        } else if (e.detail.text?.includes('Refresh Data')) {
          alert('Refresh Data clicked in App Proxy!');
          window.location.reload();
        }
      });
    }
  }, []);

  if (!isClient) {
    return (
      <div className="p-4 bg-gray-100">
        <p>Loading dashboard...</p>
      </div>
    );
  }

  return (
    <div className={`app-proxy-wrapper ${isAppProxy ? 'in-app-proxy' : 'standalone'}`}>
      {isAppProxy && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
          <strong>App Proxy Mode:</strong> Running in Shopify App Proxy context
        </div>
      )}
      {children}
    </div>
  );
}
