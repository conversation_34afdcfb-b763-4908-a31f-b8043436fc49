import type { DashboardData } from "../types/dashboard";
import WelcomeSection from "./dashboard/WelcomeSection";
import KPICard from "./dashboard/KPICard";
import TopProducts from "./dashboard/TopProducts";
import UsageBreakdown from "./dashboard/UsageBreakdown";
import RecentOrders from "./dashboard/RecentOrders";
import AIAssistant from "./dashboard/AIAssistant";
import { useEffect, useState } from "react";
import { ICustomer } from "app/types/shopify-data";

const defaultData: DashboardData = {
  user: {
    name: "<PERSON>",
    initials: "<PERSON><PERSON>",
    role: "Account Manager",
    lastLogin: "17 Jul 2025, 13:45",
  },
  kpis: {
    totalSpend: {
      value: "£24,568",
      change: "+12.5%",
      changeType: "positive",
    },
    topProducts: {
      value: 48,
      change: "+8.3%",
      changeType: "positive",
    },
    activeSites: {
      value: 12,
      change: "+0%",
      changeType: "neutral",
    },
    ordersThisMonth: {
      value: 37,
      change: "-5.1%",
      changeType: "negative",
    },
  },
  topProductsBySpend: [
    {
      name: "Professional Disinfectant",
      category: "Cleaning Chemicals",
      amount: "£3,245",
      change: "+15.2%",
      changeType: "positive",
      icon: "fa-spray-can",
    },
    {
      name: "Hand Sanitiser Gel",
      category: "Washroom Products",
      amount: "£2,890",
      change: "+8.7%",
      changeType: "positive",
      icon: "fa-soap",
    },
    {
      name: "Disposable Gloves",
      category: "PPE",
      amount: "£2,156",
      change: "-3.4%",
      changeType: "negative",
      icon: "fa-hands",
    },
    {
      name: "Industrial Mop System",
      category: "Janitorial Equipment",
      amount: "£1,890",
      change: "+12.1%",
      changeType: "positive",
      icon: "fa-broom",
    },
    {
      name: "Heavy Duty Bin Bags",
      category: "Site Consumables",
      amount: "£1,567",
      change: "+0.8%",
      changeType: "neutral",
      icon: "fa-trash",
    },
  ],
  recentOrders: [
    {
      id: "#ORD-2567",
      date: "Jul 15, 2025",
      site: "Manchester HQ",
      items: 12,
      total: "£1,245.00",
      status: "delivered",
    },
    {
      id: "#ORD-2566",
      date: "Jul 12, 2025",
      site: "Birmingham Office",
      items: 8,
      total: "£876.50",
      status: "delivered",
    },
    {
      id: "#ORD-2565",
      date: "Jul 10, 2025",
      site: "London Branch",
      items: 15,
      total: "£1,890.25",
      status: "shipped",
    },
    {
      id: "#ORD-2564",
      date: "Jul 8, 2025",
      site: "Glasgow Store",
      items: 6,
      total: "£543.75",
      status: "processing",
    },
  ],
};

export default function Dashboard({ customer }: { customer?: ICustomer }) {
  const [data, setData] = useState<DashboardData>(defaultData);

  const listLocations = customer?.companyContactProfiles?.map(profile => {
    return {
      companyName: profile.company.name,
      companyId: profile.company.id,
      locations: profile.company.locations.nodes.map(loc => ({
        id: loc.id,
        name: loc.name
      })),
      roles: profile.roleAssignments.nodes.map(role => ({
        locationId: role.companyLocation.id,
        locationName: role.companyLocation.name,
        locationExternalId: role.companyLocation.externalId,
        roleName: role.role.name
      }))
    };
  });

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <WelcomeSection
          userName={[customer?.firstName, customer?.lastName].filter(Boolean).join(' ')}
          userRole={customer?.email || ''}
          lastLogin={'17 Jul 2025, 13:45' + ' | ' + listLocations?.map(loc => loc.roles.map(l => l.locationExternalId || l.locationId)).join(' , ')}
        />

        <div className="flex">
          <div className="flex-1 pr-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <KPICard
                title="Total Spend"
                value={data.kpis.totalSpend.value}
                change={data.kpis.totalSpend.change}
                changeType={data.kpis.totalSpend.changeType}
                icon="fa-pound-sign"
                description="Compared to last quarter"
                chartId="total-spend-chart"
                chartType="areaspline"
                chartData={[
                  5000, 7500, 6800, 11200, 13500, 14800, 16500, 18200, 21000,
                  24568,
                ]}
                chartColor="#0ea5e9"
              />

              <KPICard
                title="Top Products"
                value={data.kpis.topProducts.value}
                change={data.kpis.topProducts.change}
                changeType={data.kpis.topProducts.changeType}
                icon="fa-box"
                description="Unique products purchased"
                chartId="top-products-chart"
                chartType="column"
                chartData={[32, 35, 38, 42, 45, 41, 44, 46, 48]}
                chartColor="#0284c7"
              />

              <KPICard
                title="Active Sites"
                value={data.kpis.activeSites.value}
                change={data.kpis.activeSites.change}
                changeType={data.kpis.activeSites.changeType}
                icon="fa-map-marker-alt"
                description="No change since last month"
                chartId="active-sites-chart"
                chartType="line"
                chartData={[10, 10, 11, 12, 12, 12, 12, 12, 12, 12]}
                chartColor="#0369a1"
              />

              <KPICard
                title="Orders This Month"
                value={data.kpis.ordersThisMonth.value}
                change={data.kpis.ordersThisMonth.change}
                changeType={data.kpis.ordersThisMonth.changeType}
                icon="fa-shopping-cart"
                description="Compared to last month"
                chartId="orders-chart"
                chartType="column"
                chartData={[42, 40, 38, 45, 43, 39, 41, 38, 39, 37]}
                chartColor="#075985"
              />
            </div>

            <TopProducts products={data.topProductsBySpend} />

            <UsageBreakdown />

            <RecentOrders orders={data.recentOrders} />
          </div>

          <AIAssistant
            userName={data.user.name}
            userInitials={data.user.initials}
          />
        </div>
    </div>
  );
}