import { useEffect, useState } from "react";

interface WelcomeSectionProps {
  userName: string;
  userRole: string;
  lastLogin: string;
}

export default function WelcomeSection({
  userName,
  userRole,
  lastLogin,
}: WelcomeSectionProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    console.log("WelcomeSection mounted on client");
  }, []);

  const handleDownloadReport = () => {
    console.log("Download Report 2 - Client side");
    alert("Download Report clicked!");

    // Additional debugging
    console.log("Event triggered successfully");
    console.log("Current environment:", typeof window !== 'undefined' ? 'browser' : 'server');
    console.log("User agent:", typeof navigator !== 'undefined' ? navigator.userAgent : 'N/A');
  };

  const handleRefreshData = () => {
    console.log("Refresh Data clicked");
    alert("Refresh Data clicked!");

    // Force page reload as test
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  };

  // Add debugging info
  useEffect(() => {
    console.log("WelcomeSection props:", { userName, userRole, lastLogin });
    console.log("Is client:", isClient);
  }, [userName, userRole, lastLogin, isClient]);

  return (
    <div className="mb-8 flex justify-between">
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">
          Welcome back, {userName}
        </h1>
        <p className="text-gray-600">
          {userRole} | Last login: {lastLogin}
        </p>
        {/* Debug info */}
        <p className="text-xs text-gray-400 mt-1">
          Client: {isClient ? 'Yes' : 'No'} | Env: {typeof window !== 'undefined' ? 'Browser' : 'Server'}
        </p>
      </div>
      <div className="flex items-center">
        <div className="relative">
          <button
            onClick={handleDownloadReport}
            onMouseDown={() => console.log("Mouse down on download button")}
            onMouseUp={() => console.log("Mouse up on download button")}
            className="flex items-center space-x-2 bg-white border border-gray-300 rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 cursor-pointer"
            style={{ pointerEvents: 'auto' }}
          >
            <span>Download Report 2</span>
            <i className="fa-solid fa-chevron-down"></i>
          </button>
        </div>
        <button
          onClick={handleRefreshData}
          onMouseDown={() => console.log("Mouse down on refresh button")}
          onMouseUp={() => console.log("Mouse up on refresh button")}
          className="ml-4 bg-msc-teal hover:bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium cursor-pointer"
          style={{ pointerEvents: 'auto' }}
        >
          Refresh Data
        </button>
      </div>
    </div>
  );
}
