interface WelcomeSectionProps {
  userName: string;
  userRole: string;
  lastLogin: string;
}

export default function WelcomeSection({
  userName,
  userRole,
  lastLogin,
}: WelcomeSectionProps) {
  return (
    <div className="mb-8 flex justify-between">
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">
          Welcome back, {userName}
        </h1>
        <p className="text-gray-600">
          {userRole} | Last login: {lastLogin}
        </p>
      </div>
      <div className="flex items-center">
        <div className="relative">
          <button
            onClick={() => {
              console.log("Download Report 2");
            }}
            className="flex items-center space-x-2 bg-white border border-gray-300 rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            <span>Download Report 2</span>
            <i className="fa-solid fa-chevron-down"></i>
          </button>
        </div>
        <button className="ml-4 bg-msc-teal hover:bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium">
          Refresh Data
        </button>
      </div>
    </div>
  );
}
